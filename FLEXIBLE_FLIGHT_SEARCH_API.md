# Flexible Flight Search API

## Overview

The Flexible Flight Search API allows users to search for flights with flexible dates around their preferred departure and return dates. This is useful for finding the best prices by exploring alternative travel dates.

## Endpoint

```
POST /api/flights/search/flexible-search
```

## Request Body

```json
{
  "origin": "NYC",
  "destination": "LAX", 
  "departure_date": "2025-07-20",
  "return_date": "2025-07-22",
  "passengers": [
    {"type": "adult"}
  ],
  "cabin_class": "economy",
  "flexible_days": 7
}
```

**Or without return_date (will default to 7 days after departure):**

```json
{
  "origin": "NYC",
  "destination": "LAX", 
  "departure_date": "2025-07-20",
  "passengers": [
    {"type": "adult"}
  ],
  "cabin_class": "economy",
  "flexible_days": 7
}
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `origin` | string | Yes | 3-letter airport code for departure |
| `destination` | string | Yes | 3-letter airport code for arrival |
| `departure_date` | date | Yes | Preferred departure date (YYYY-MM-DD) |
| `return_date` | date | No | Preferred return date (YYYY-MM-DD). If not provided, defaults to 7 days after departure date |
| `passengers` | array | Yes | Array of passenger objects |
| `passengers[].type` | string | Yes* | Passenger type: "adult", "child", "infant" |
| `passengers[].age` | integer | Yes* | Passenger age (0-120) |
| `cabin_class` | string | No | Cabin class: "economy", "premium_economy", "business", "first" |
| `flexible_days` | integer | Yes | Number of days before/after preferred dates to search (1-14) |
| `provider` | string | No | Flight provider (default: "duffel") |

*Either `type` or `age` must be specified for each passenger.

## Response

```json
{
  "success": true,
  "data": {
    "flexible_dates": [
      {
        "departure_date": "2025-07-19",
        "return_date": "2025-07-21", 
        "price": "363.00",
        "currency": "USD",
        "available": true
      },
      {
        "departure_date": "2025-07-20",
        "return_date": "2025-07-22",
        "price": "394.00", 
        "currency": "USD",
        "available": true
      },
      {
        "departure_date": "2025-07-21",
        "return_date": "2025-07-23",
        "price": null,
        "currency": "USD", 
        "available": false
      }
    ]
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Whether the request was successful |
| `data.flexible_dates` | array | Array of available date combinations |
| `flexible_dates[].departure_date` | string | Departure date (YYYY-MM-DD) |
| `flexible_dates[].return_date` | string | Return date (YYYY-MM-DD) |
| `flexible_dates[].price` | string|null | Price in the specified currency |
| `flexible_dates[].currency` | string | Currency code (e.g., "USD") |
| `flexible_dates[].available` | boolean | Whether flights are available for this date combination |

## Example Usage

### cURL

```bash
# With return_date
curl -X POST http://127.0.0.1:8000/api/flights/search/flexible-search \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "origin": "NYC",
    "destination": "LAX",
    "departure_date": "2025-07-20",
    "return_date": "2025-07-22",
    "passengers": [
      {"type": "adult"}
    ],
    "cabin_class": "economy",
    "flexible_days": 7
  }'

# Without return_date (defaults to 7 days after departure)
curl -X POST http://127.0.0.1:8000/api/flights/search/flexible-search \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "origin": "NYC",
    "destination": "LAX",
    "departure_date": "2025-07-20",
    "passengers": [
      {"type": "adult"}
    ],
    "cabin_class": "economy",
    "flexible_days": 7
  }'
```

### JavaScript

```javascript
// With return_date
const response = await fetch('/api/flights/search/flexible-search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify({
    origin: 'NYC',
    destination: 'LAX',
    departure_date: '2025-07-20',
    return_date: '2025-07-22',
    passengers: [{ type: 'adult' }],
    cabin_class: 'economy',
    flexible_days: 7
  })
});

// Without return_date (defaults to 7 days after departure)
const response2 = await fetch('/api/flights/search/flexible-search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify({
    origin: 'NYC',
    destination: 'LAX',
    departure_date: '2025-07-20',
    passengers: [{ type: 'adult' }],
    cabin_class: 'economy',
    flexible_days: 7
  })
});

const data = await response.json();
console.log(data.data.flexible_dates);
```

## Error Responses

### Validation Error

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "departure_date": ["Departure date must be today or in the future"],
    "return_date": ["Return date must be after departure date"]
  }
}
```

### Server Error

```json
{
  "success": false,
  "message": "Flexible flight search failed",
  "error": "Error details"
}
```

## Performance Notes

- The API is optimized to limit the number of date combinations to prevent timeouts
- Maximum flexible days is limited to 3 for performance reasons
- Maximum combinations returned is 9 (3x3 grid)
- For production use, consider implementing caching and async processing for large date ranges

## Implementation Details

The flexible flight search works by:

1. Generating date ranges around the preferred departure and return dates
2. Creating all valid date combinations (where return date > departure date)
3. Searching for flights for each date combination
4. Returning the cheapest available price for each combination
5. Marking combinations as unavailable if no flights are found

The service uses mock data for testing purposes. In production, it would make actual API calls to flight providers. 