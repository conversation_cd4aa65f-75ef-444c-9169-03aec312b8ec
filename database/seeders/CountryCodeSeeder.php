<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CountryCode;

class CountryCodeSeeder extends Seeder
{
    public function run(): void
    {
        $countries = [
            ['name' => 'Iran', 'iso2' => 'IR', 'iso3' => 'IRN', 'phone_code' => '+98', 'active' => true],
            ['name' => 'Turkey', 'iso2' => 'TR', 'iso3' => 'TUR', 'phone_code' => '+90', 'active' => true],
            ['name' => 'United Arab Emirates', 'iso2' => 'AE', 'iso3' => 'ARE', 'phone_code' => '+971', 'active' => true],
            ['name' => 'United States', 'iso2' => 'US', 'iso3' => 'USA', 'phone_code' => '+1', 'active' => true],
            ['name' => 'Germany', 'iso2' => 'DE', 'iso3' => 'DEU', 'phone_code' => '+49', 'active' => true],
        ];
        foreach ($countries as $country) {
            CountryCode::updateOrCreate([
                'iso2' => $country['iso2']
            ], $country);
        }
    }
} 