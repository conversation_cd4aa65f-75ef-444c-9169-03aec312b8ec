<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Passenger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PassengerController extends Controller
{
    // لیست مسافران کاربر یا مهمان
    public function index(Request $request)
    {
        if (Auth::check()) {
            $passengers = Passenger::where('user_id', Auth::id())->get();
        } else {
            $guestToken = $request->query('guest_token');
            $passengers = $guestToken ? Passenger::where('guest_token', $guestToken)->get() : collect();
        }
        return response()->json(['success' => true, 'data' => $passengers]);
    }

    // ثبت مسافر جدید
    public function store(Request $request)
    {
        $validated = $request->validate([
            'given_name' => 'required|string|max:50',
            'family_name' => 'required|string|max:50',
            'national_id' => 'nullable|string|max:20',
            'passport_number' => 'nullable|string|max:30',
            'passport_expiry_date' => 'nullable|date',
            'birthdate' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'guest_token' => 'nullable|string',
            'nationality' => 'nullable|string|max:100',
        ]);

        if (Auth::check()) {
            $validated['user_id'] = Auth::id();
            $validated['guest_token'] = null;
        } else {
            $validated['user_id'] = null;
            $validated['guest_token'] = $validated['guest_token'] ?? Str::uuid()->toString();
        }

        $passenger = Passenger::create($validated);
        return response()->json(['success' => true, 'data' => $passenger]);
    }

    // اتصال مسافران مهمان به کاربر بعد از لاگین
    public function attachGuestPassengers(Request $request)
    {
        $request->validate(['guest_token' => 'required|string']);
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }
        $guestToken = $request->input('guest_token');
        Passenger::where('guest_token', $guestToken)
            ->update(['user_id' => Auth::id(), 'guest_token' => null]);
        return response()->json(['success' => true]);
    }

    // ویرایش مسافر
    public function update(Request $request, $id)
    {
        $passenger = Passenger::findOrFail($id);
        // فقط اگر متعلق به کاربر فعلی یا guest_token فعلی باشد
        if (Auth::check()) {
            if ($passenger->user_id !== Auth::id()) {
                return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
            }
        } else {
            $guestToken = $request->input('guest_token');
            if (!$guestToken || $passenger->guest_token !== $guestToken) {
                return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
            }
        }
        $validated = $request->validate([
            'given_name' => 'sometimes|required|string|max:50',
            'family_name' => 'sometimes|required|string|max:50',
            'national_id' => 'nullable|string|max:20',
            'passport_number' => 'nullable|string|max:30',
            'passport_expiry_date' => 'nullable|date',
            'birthdate' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'nationality' => 'nullable|string|max:100',
        ]);
        $passenger->update($validated);
        return response()->json(['success' => true, 'data' => $passenger]);
    }

    // حذف مسافر
    public function destroy(Request $request, $id)
    {
        $passenger = Passenger::findOrFail($id);
        // فقط اگر متعلق به کاربر فعلی یا guest_token فعلی باشد
        if (Auth::check()) {
            if ($passenger->user_id !== Auth::id()) {
                return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
            }
        } else {
            $guestToken = $request->input('guest_token');
            if (!$guestToken || $passenger->guest_token !== $guestToken) {
                return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
            }
        }
        $passenger->delete();
        return response()->json(['success' => true]);
    }
} 