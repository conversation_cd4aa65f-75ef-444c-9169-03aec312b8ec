<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        $validated = $request->validate([
            'email' => 'nullable|email',
            'mobile' => 'nullable|string',
            'country_code' => 'nullable|string',
            'password' => 'required|string',
        ]);

        // ورود با ایمیل
        if (!empty($validated['email'])) {
            $user = User::where('email', $validated['email'])->first();
        } elseif (!empty($validated['mobile']) && !empty($validated['country_code'])) {
            $user = User::where('mobile', $validated['mobile'])
                ->where('country_code', $validated['country_code'])
                ->first();
        } else {
            return response()->json(['success' => false, 'message' => 'Email or mobile and country_code required'], 422);
        }

        if (!$user || !Hash::check($validated['password'], $user->password)) {
            return response()->json(['success' => false, 'message' => 'Invalid credentials'], 401);
        }

        // اگر نیاز به توکن دارید:
        $token = $user->createToken('api')->plainTextToken;

        return response()->json([
            'success' => true,
            'data' => $user,
            'token' => $token,
        ]);
    }
} 