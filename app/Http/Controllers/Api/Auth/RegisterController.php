<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class RegisterController extends Controller
{
    public function register(Request $request)
    {
        $validated = $request->validate([
            'email' => ['required', 'email', 'max:255', 'unique:users,email'],
            'mobile' => ['required', 'string', 'max:20', 'unique:users,mobile'],
            'country_code' => ['required', 'string', 'max:8', Rule::exists('country_codes', 'phone_code')],
            'contact_name' => ['required', 'string', 'max:100'],
            'password' => ['required', 'string', 'min:6'],
        ]);

        $user = User::create([
            'email' => $validated['email'],
            'mobile' => $validated['mobile'],
            'country_code' => $validated['country_code'],
            'contact_name' => $validated['contact_name'],
            'name' => $validated['contact_name'], // اضافه شد
            'password' => Hash::make($validated['password']),
        ]);

        // اگر نیاز به توکن دارید:
        $token = $user->createToken('api')->plainTextToken;

        return response()->json([
            'success' => true,
            'data' => $user,
            'token' => $token,
        ]);
    }

    public function validateEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        $exists = \App\Models\User::where('email', $request->input('email'))->exists();
        return response()->json([
            'success' => true,
            'registered' => $exists,
        ]);
    }
} 