<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FlexibleFlightSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'origin' => 'required|string|size:3',
            'destination' => 'required|string|size:3',
            'departure_date' => 'required|date|after_or_equal:today',
            'return_date' => 'sometimes|date|after:departure_date',
            'passengers' => 'required|array|min:1|max:9',
            'passengers.*.type' => 'required_without:passengers.*.age|string|in:adult,child,infant',
            'passengers.*.age' => 'required_without:passengers.*.type|integer|min:0|max:120',
            'passengers.*.given_name' => 'sometimes|string|max:50',
            'passengers.*.family_name' => 'sometimes|string|max:50',
            'passengers.*.fare_type' => 'sometimes|string',
            'passengers.*.loyalty_programme_accounts' => 'sometimes|array',
            'passengers.*.loyalty_programme_accounts.*.airline_iata_code' => 'required_with:passengers.*.loyalty_programme_accounts|string|size:2',
            'passengers.*.loyalty_programme_accounts.*.account_number' => 'required_with:passengers.*.loyalty_programme_accounts|string|max:50',
            'cabin_class' => 'sometimes|string|in:first,business,premium_economy,economy',
            'flexible_days' => 'required|integer|min:1|max:14',
            'provider' => 'sometimes|string|in:duffel',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'origin.required' => 'Origin airport code is required',
            'origin.size' => 'Origin must be a 3-letter airport code',
            'destination.required' => 'Destination airport code is required',
            'destination.size' => 'Destination must be a 3-letter airport code',
            'departure_date.required' => 'Departure date is required',
            'departure_date.after_or_equal' => 'Departure date must be today or in the future',
            'return_date.after' => 'Return date must be after departure date',
            'passengers.required' => 'At least one passenger is required',
            'passengers.min' => 'At least one passenger is required',
            'passengers.max' => 'Maximum 9 passengers allowed',
            'passengers.*.type.required_without' => 'Either passenger type or age must be specified',
            'passengers.*.age.required_without' => 'Either passenger type or age must be specified',
            'flexible_days.required' => 'Flexible days is required',
            'flexible_days.min' => 'Flexible days must be at least 1',
            'flexible_days.max' => 'Flexible days cannot exceed 14',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'origin' => 'origin airport',
            'destination' => 'destination airport',
            'departure_date' => 'departure date',
            'return_date' => 'return date',
            'passengers.*.type' => 'passenger type',
            'passengers.*.age' => 'passenger age',
            'passengers.*.given_name' => 'passenger given name',
            'passengers.*.family_name' => 'passenger family name',
            'flexible_days' => 'flexible days',
        ];
    }
} 