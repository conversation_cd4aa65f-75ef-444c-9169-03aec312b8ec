<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Airport extends Model
{
    use HasFactory;

    protected $fillable = [
        'iata_code',
        'icao_code',
        'name',
        'city_name',
        'iata_city_code',
        'iata_country_code',
        'latitude',
        'longitude',
        'time_zone',
        'type',
        'city_data',
        'provider',
        'external_id',
        'is_major',
    ];

    protected $casts = [
        'city_data' => 'array',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'is_major' => 'boolean',
    ];

    /**
     * Scope for major airports only
     */
    public function scopeMajor($query)
    {
        return $query->where('is_major', true);
    }

    /**
     * Scope for searching by query
     */
    public function scopeSearch($query, $searchTerm)
    {
        $searchTerm = trim($searchTerm);
        
        return $query->where(function ($q) use ($searchTerm) {
            // Exact IATA code match (highest priority)
            $q->where('iata_code', '=', strtoupper($searchTerm))
              // Partial IATA code match
              ->orWhere('iata_code', 'like', strtoupper($searchTerm) . '%')
              // Exact ICAO code match
              ->orWhere('icao_code', '=', strtoupper($searchTerm))
              // Partial ICAO code match
              ->orWhere('icao_code', 'like', strtoupper($searchTerm) . '%')
              // Airport name contains search term
              ->orWhere('name', 'like', "%{$searchTerm}%")
              // City name contains search term
              ->orWhere('city_name', 'like', "%{$searchTerm}%")
              // City name starts with search term (higher priority)
              ->orWhere('city_name', 'like', "{$searchTerm}%")
              // IATA city code match
              ->orWhere('iata_city_code', '=', strtoupper($searchTerm))
              // Partial IATA city code match
              ->orWhere('iata_city_code', 'like', strtoupper($searchTerm) . '%');
        });
    }

    /**
     * Scope for filtering by country
     */
    public function scopeCountry($query, $countryCode)
    {
        return $query->where('iata_country_code', $countryCode);
    }
}
