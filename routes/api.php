<?php

use App\Http\Controllers\Api\FlightController;
use App\Http\Controllers\Api\TravelServiceController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Travel Service Management Routes
|--------------------------------------------------------------------------
*/

Route::prefix('travel')->group(function () {
    // Service status and testing
    Route::get('/providers', [TravelServiceController::class, 'getProviders']);
    Route::get('/providers/{provider}/test', [TravelServiceController::class, 'testProvider']);
    Route::get('/providers/{provider}/stats', [TravelServiceController::class, 'getProviderStats']);
    Route::post('/providers/test-all', [TravelServiceController::class, 'testAllProviders']);
});

/*
|--------------------------------------------------------------------------
| Flight API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('flights')->group(function () {
    // Flight search
    Route::post('/search', [FlightController::class, 'search']);
    Route::post('/search/flexible-search', [FlightController::class, 'searchFlexible']);
    Route::get('/searches/{search}', [FlightController::class, 'getSearch']);
    Route::get('/searches', [FlightController::class, 'getSearches']);
    
    // Flight offers
    Route::get('/offers/{offer}', [FlightController::class, 'getOffer']);
    Route::get('/offers/{offer}/details', [FlightController::class, 'getOfferDetails']);
    Route::get('/searches/{search}/offers', [FlightController::class, 'getSearchOffers']);
    Route::post('/searches/{search}/offers/filter', [FlightController::class, 'getSearchOffers']);
    
    // Flight booking
    Route::post('/book', [FlightController::class, 'book']);
    Route::get('/bookings/{booking}', [FlightController::class, 'getBooking']);
    Route::get('/bookings', [FlightController::class, 'getBookings']);
    Route::post('/bookings/{booking}/cancel', [FlightController::class, 'cancelBooking']);
    
    // Reference data
    Route::get('/airports', [FlightController::class, 'getAirports']);
    Route::get('/airports/all', [FlightController::class, 'getAllAirports']);
    Route::get('/airports/search', [FlightController::class, 'searchAirports']);
    Route::get('/airlines', [FlightController::class, 'getAirlines']);
    Route::get('/showfarefirst', [FlightController::class, 'showFareFirst']);
    Route::get('/quicksearch', [FlightController::class, 'quickSearch']);
});

/*
|--------------------------------------------------------------------------
| Provider-specific Routes (Optional)
|--------------------------------------------------------------------------
*/

Route::prefix('duffel')->group(function () {
    Route::post('/flights/search', [FlightController::class, 'search'])->defaults('provider', 'duffel');
    Route::get('/flights/offers/{offer}', [FlightController::class, 'getOffer'])->defaults('provider', 'duffel');
    Route::post('/flights/book', [FlightController::class, 'book'])->defaults('provider', 'duffel');
});

// Future provider routes can be added here
// Route::prefix('amadeus')->group(function () {
//     Route::post('/flights/search', [FlightController::class, 'search'])->defaults('provider', 'amadeus');
//     Route::get('/flights/offers/{offer}', [FlightController::class, 'getOffer'])->defaults('provider', 'amadeus');
//     Route::post('/flights/book', [FlightController::class, 'book'])->defaults('provider', 'amadeus');
// });

Route::post('/register', [\App\Http\Controllers\Api\Auth\RegisterController::class, 'register']);
Route::post('/login', [\App\Http\Controllers\Api\Auth\LoginController::class, 'login']);
Route::post('/validate-email', [\App\Http\Controllers\Api\Auth\RegisterController::class, 'validateEmail']);
Route::get('/passengers', [\App\Http\Controllers\Api\PassengerController::class, 'index']);
Route::post('/passengers', [\App\Http\Controllers\Api\PassengerController::class, 'store']);
Route::post('/passengers/attach-guest', [\App\Http\Controllers\Api\PassengerController::class, 'attachGuestPassengers']);
Route::match(['put', 'patch'], '/passengers/{id}', [\App\Http\Controllers\Api\PassengerController::class, 'update']);
Route::delete('/passengers/{id}', [\App\Http\Controllers\Api\PassengerController::class, 'destroy']);
